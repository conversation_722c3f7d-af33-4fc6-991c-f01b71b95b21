package dao_explosive

import (
	"activitysrv/config"
	"activitysrv/internal/model"
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
)

// CycleDAO 活动周期数据访问对象
type CycleDAO struct{}

// NewCycleDAO 创建新的周期DAO
func NewCycleDAO() *CycleDAO {
	return &CycleDAO{}
}

// GetCurrentCycle 获取当前活动周期
func (dao *CycleDAO) GetCurrentCycle(ctx context.Context, activityId int64) (*model.ActivityCycle, error) {
	key := config.ActivityCycleKey(activityId)
	redisCli := redisx.GetRedisCli("activity", 0)

	// 使用 HGET 获取周期数据
	data, err := redisCli.HGet(ctx, key, "cycle").Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			// 当前周期不存在，需要创建
			return nil, nil
		}
		return nil, fmt.Errorf("获取当前周期失败: %w", err)
	}

	// 获取结束时间
	endTimeStr, err := redisCli.HGet(ctx, key, "end_time").Result()
	if err != nil {
		return nil, fmt.Errorf("获取周期结束时间失败: %w", err)
	}

	// 解析数据
	cycleId, err := strconv.ParseInt(data, 10, 32)
	if err != nil {
		return nil, fmt.Errorf("解析周期ID失败: %w", err)
	}

	endTime, err := strconv.ParseInt(endTimeStr, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("解析结束时间失败: %w", err)
	}

	// 构建周期对象
	cycle := &model.ActivityCycle{
		CycleId:   int32(cycleId),
		EndTime:   endTime,
		Status:    1, // 活跃状态
		CreatedAt: time.Now().Unix(),
	}

	return cycle, nil
}

// CreateNewCycle 创建新的活动周期
func (dao *CycleDAO) CreateNewCycle(ctx context.Context, activityId int64, cycleDays int32) (*model.ActivityCycle, error) {
	// 使用分布式锁防止并发创建
	lockKey := config.ExplosiveProtectionCycleLockKey(activityId)
	redisCli := redisx.GetRedisCli("activity", 0)

	// 尝试获取锁
	lockValue := fmt.Sprintf("%d_%d", time.Now().UnixNano(), activityId)
	acquired, err := redisCli.SetNX(ctx, lockKey, lockValue, config.ExplosiveProtectionCycleLockExpire).Result()
	if err != nil {
		return nil, fmt.Errorf("获取分布式锁失败: %w", err)
	}
	if !acquired {
		return nil, fmt.Errorf("其他进程正在创建周期，请稍后重试")
	}

	// 确保释放锁
	defer func() {
		redisCli.Del(ctx, lockKey)
	}()

	// 再次检查是否已经有当前周期（双重检查）
	existingCycle, err := dao.GetCurrentCycle(ctx, activityId)
	if err != nil {
		return nil, err
	}
	if existingCycle != nil {
		return existingCycle, nil
	}

	// 获取下一个周期ID
	nextCycleId, err := dao.getNextCycleId(ctx, activityId)
	if err != nil {
		return nil, fmt.Errorf("获取下一个周期ID失败: %w", err)
	}

	// 计算周期时间
	now := time.Now()
	startTime := now.Unix()
	endTime := now.Add(time.Duration(cycleDays) * 24 * time.Hour).Unix()

	// 创建新周期
	newCycle := model.NewActivityCycle(nextCycleId, startTime, endTime)

	// 保存到Redis
	if err := dao.saveCurrentCycle(ctx, activityId, newCycle); err != nil {
		return nil, fmt.Errorf("保存当前周期失败: %w", err)
	}

	// 注意：新设计不再需要历史记录，直接使用当前周期数据

	logrus.Infof("成功创建新的活动周期: activityId=%d, cycleId=%d, startTime=%d, endTime=%d",
		activityId, newCycle.CycleId, newCycle.StartTime, newCycle.EndTime)

	return newCycle, nil
}

// CheckAndCreateCycleIfNeeded 检查并在需要时创建新周期
func (dao *CycleDAO) CheckAndCreateCycleIfNeeded(ctx context.Context, activityId int64, cycleDays int32) (*model.ActivityCycle, error) {
	currentCycle, err := dao.GetCurrentCycle(ctx, activityId)
	if err != nil {
		return nil, err
	}

	// 如果没有当前周期或当前周期已过期，创建新周期
	if currentCycle == nil || currentCycle.IsExpired() {
		// 注意：新设计中旧周期会被新周期自动覆盖

		return dao.CreateNewCycle(ctx, activityId, cycleDays)
	}

	return currentCycle, nil
}

// getNextCycleId 获取下一个周期ID
func (dao *CycleDAO) getNextCycleId(ctx context.Context, activityId int64) (int32, error) {
	key := config.ActivityCycleKey(activityId)
	redisCli := redisx.GetRedisCli("activity", 0)

	// 尝试从当前周期获取最大ID
	currentCycleStr, err := redisCli.HGet(ctx, key, "cycle").Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		return 0, fmt.Errorf("获取当前周期ID失败: %w", err)
	}

	if errors.Is(err, redis.Nil) {
		// 没有当前周期，从1开始
		return 1, nil
	}

	// 解析当前周期ID
	currentCycleId, err := strconv.ParseInt(currentCycleStr, 10, 32)
	if err != nil {
		return 0, fmt.Errorf("解析当前周期ID失败: %w", err)
	}

	return int32(currentCycleId) + 1, nil
}

// saveCurrentCycle 保存当前周期到Redis (按照需求文档格式)
func (dao *CycleDAO) saveCurrentCycle(ctx context.Context, activityId int64, cycle *model.ActivityCycle) error {
	key := config.ActivityCycleKey(activityId)
	redisCli := redisx.GetRedisCli("activity", 0)

	// 计算剩余天数
	now := time.Now().Unix()
	remainingDays := int32((cycle.EndTime - now) / (24 * 3600))
	if remainingDays < 0 {
		remainingDays = 0
	}

	// 计算周期天数
	cycleDays := int32((cycle.EndTime - cycle.StartTime) / (24 * 3600))

	// 计算TTL (当前周期剩余天数 + 周期天数 + 1 + 随机1天)
	ttl := config.CalculateActivityTTL(cycleDays, remainingDays)

	// 使用 HSET 保存数据
	pipe := redisCli.Pipeline()
	pipe.HSet(ctx, key, "cycle", cycle.CycleId)
	pipe.HSet(ctx, key, "end_time", cycle.EndTime)
	pipe.Expire(ctx, key, ttl)

	_, err := pipe.Exec(ctx)
	return err
}

// GetPreviousCycle 获取上一个周期数据 (用于奖励领取)
func (dao *CycleDAO) GetPreviousCycle(ctx context.Context, activityId int64) (*model.ActivityCycle, error) {
	// 获取当前周期
	currentCycle, err := dao.GetCurrentCycle(ctx, activityId)
	if err != nil {
		return nil, err
	}

	if currentCycle == nil || currentCycle.CycleId <= 1 {
		// 没有当前周期或当前是第一个周期，没有上一个周期
		return nil, nil
	}

	// 构建上一个周期数据 (简化实现，实际可能需要更复杂的逻辑)
	previousCycle := &model.ActivityCycle{
		CycleId:   currentCycle.CycleId - 1,
		StartTime: currentCycle.StartTime - (currentCycle.EndTime - currentCycle.StartTime),
		EndTime:   currentCycle.StartTime,
		Status:    2, // 已结束状态
		CreatedAt: currentCycle.CreatedAt,
	}

	return previousCycle, nil
}
