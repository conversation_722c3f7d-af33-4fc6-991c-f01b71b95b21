package dao_explosive

import (
	"activitysrv/internal/model"
	"context"
	"testing"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/testx"
	"github.com/stretchr/testify/assert"
)

func TestUserDataDAO_GetUserData(t *testing.T) {
	// 初始化测试环境
	testx.Init()
	ctx := testx.TestCtx(1, 1)

	activityId := int64(1001)
	userId := uint64(12345)
	cycleId := int32(1)

	// 第一次获取，应该返回初始化的数据
	userData, err := GetUserData(ctx, activityId, userId, cycleId)

	// 验证结果
	assert.NoError(t, err, "获取用户数据应该成功")
	assert.NotNil(t, userData, "用户数据不应为空")
	assert.NotNil(t, userData.Metrics, "指标数据不应为空")
	assert.NotNil(t, userData.ClaimedStages, "已领取阶段不应为空")
	assert.Equal(t, 0, len(userData.Metrics), "初始指标数据应该为空")
	assert.Equal(t, 0, len(userData.ClaimedStages), "初始已领取阶段应该为空")
}

func TestUserDataDAO_SaveUserData(t *testing.T) {
	// 初始化测试环境
	testx.Init()
	ctx := testx.TestCtx(1, 1)

	activityId := int64(1001)
	userId := uint64(12345)
	cycleId := int32(1)

	// 创建用户数据
	userData := model.NewUserActivityData()
	userData.UpdateMetric(model.MetricTypeFishWeight, 1500, model.MetricOperationAdd)
	userData.UpdateMetric(model.MetricTypeFishCount, 1, model.MetricOperationAdd)
	userData.ClaimStage(1, `[{"item_id":1001,"count":100}]`)

	// 保存用户数据
	err := SaveUserData(ctx, activityId, userId, cycleId, userData)
	assert.NoError(t, err, "保存用户数据应该成功")

	// 重新获取数据验证
	savedData, err := GetUserData(ctx, activityId, userId, cycleId)
	assert.NoError(t, err, "获取保存的用户数据应该成功")
	assert.Equal(t, int64(1500), savedData.Metrics[model.MetricTypeFishWeight], "鱼重量指标应该正确")
	assert.Equal(t, int64(1), savedData.Metrics[model.MetricTypeFishCount], "鱼数量指标应该正确")
	assert.True(t, savedData.IsStageClaimedStage(1), "阶段1应该已领取")
}

func TestUserDataDAO_UpdateUserMetrics(t *testing.T) {
	// 初始化测试环境
	testx.Init()
	ctx := testx.TestCtx(1, 1)

	activityId := int64(1001)
	userId := uint64(12345)
	cycleId := int32(1)

	// 第一次更新指标
	metricUpdates1 := map[int32]int64{
		model.MetricTypeFishWeight: 1000,
		model.MetricTypeFishCount:  1,
	}

	err := UpdateUserMetrics(ctx, activityId, userId, cycleId, metricUpdates1, model.MetricOperationAdd)
	assert.NoError(t, err, "更新用户指标应该成功")

	// 验证第一次更新结果
	userData, err := GetUserData(ctx, activityId, userId, cycleId)
	assert.NoError(t, err, "获取用户数据应该成功")
	assert.Equal(t, int64(1000), userData.Metrics[model.MetricTypeFishWeight], "鱼重量应该是1000")
	assert.Equal(t, int64(1), userData.Metrics[model.MetricTypeFishCount], "鱼数量应该是1")

	// 第二次更新指标（累加）
	metricUpdates2 := map[int32]int64{
		model.MetricTypeFishWeight: 500,
		model.MetricTypeFishCount:  1,
	}

	err = UpdateUserMetrics(ctx, activityId, userId, cycleId, metricUpdates2, model.MetricOperationAdd)
	assert.NoError(t, err, "第二次更新用户指标应该成功")

	// 验证第二次更新结果
	userData, err = GetUserData(ctx, activityId, userId, cycleId)
	assert.NoError(t, err, "获取用户数据应该成功")
	assert.Equal(t, int64(1500), userData.Metrics[model.MetricTypeFishWeight], "鱼重量应该是1500")
	assert.Equal(t, int64(2), userData.Metrics[model.MetricTypeFishCount], "鱼数量应该是2")
}

func TestUserDataDAO_ClaimReward(t *testing.T) {
	// 初始化测试环境
	testx.Init()
	ctx := testx.TestCtx(1, 1)

	activityId := int64(1001)
	userId := uint64(12345)
	cycleId := int32(1)
	stageId := int32(1)
	rewardConfig := `[{"item_id":1001,"count":100}]`

	// 领取奖励
	err := ClaimReward(ctx, activityId, userId, cycleId, stageId, rewardConfig)
	assert.NoError(t, err, "领取奖励应该成功")

	// 验证领取结果
	userData, err := GetUserData(ctx, activityId, userId, cycleId)
	assert.NoError(t, err, "获取用户数据应该成功")
	assert.True(t, userData.IsStageClaimedStage(stageId), "阶段应该已领取")

	// 尝试重复领取，应该失败
	err = ClaimReward(ctx, activityId, userId, cycleId, stageId, rewardConfig)
	assert.Error(t, err, "重复领取奖励应该失败")
	assert.Contains(t, err.Error(), "已经领取过奖励", "错误信息应该包含已领取提示")
}

func TestUserDataDAO_GetUserDataBatch(t *testing.T) {
	// 初始化测试环境
	testx.Init()
	ctx := testx.TestCtx(1, 1)

	activityId := int64(1001)
	userId := uint64(12345)
	cycleIds := []int32{1, 2, 3}

	// 为第一个周期添加一些数据
	metricUpdates := map[int32]int64{
		model.MetricTypeFishWeight: 1000,
	}
	err := UpdateUserMetrics(ctx, activityId, userId, 1, metricUpdates, model.MetricOperationAdd)
	assert.NoError(t, err, "更新第一个周期数据应该成功")

	// 批量获取数据
	userDataMap, err := GetUserDataBatch(ctx, activityId, userId, cycleIds)
	assert.NoError(t, err, "批量获取用户数据应该成功")
	assert.Equal(t, 3, len(userDataMap), "应该返回3个周期的数据")

	// 验证第一个周期有数据
	assert.Equal(t, int64(1000), userDataMap[1].Metrics[model.MetricTypeFishWeight], "第一个周期应该有数据")

	// 验证其他周期是初始化数据
	assert.Equal(t, 0, len(userDataMap[2].Metrics), "第二个周期应该是初始化数据")
	assert.Equal(t, 0, len(userDataMap[3].Metrics), "第三个周期应该是初始化数据")
}

func TestUserDataDAO_DeleteUserData(t *testing.T) {
	// 初始化测试环境
	testx.Init()
	ctx := testx.TestCtx(1, 1)

	activityId := int64(1001)
	userId := uint64(12345)
	cycleId := int32(1)

	// 先添加一些数据
	metricUpdates := map[int32]int64{
		model.MetricTypeFishWeight: 1000,
	}
	err := UpdateUserMetrics(ctx, activityId, userId, cycleId, metricUpdates, model.MetricOperationAdd)
	assert.NoError(t, err, "更新用户指标应该成功")

	// 验证数据存在
	userData, err := GetUserData(ctx, activityId, userId, cycleId)
	assert.NoError(t, err, "获取用户数据应该成功")
	assert.Equal(t, int64(1000), userData.Metrics[model.MetricTypeFishWeight], "数据应该存在")

	// 删除数据
	err = DeleteUserData(ctx, activityId, userId, cycleId)
	assert.NoError(t, err, "删除用户数据应该成功")

	// 验证数据已删除（应该返回初始化数据）
	userData, err = GetUserData(ctx, activityId, userId, cycleId)
	assert.NoError(t, err, "获取用户数据应该成功")
	assert.Equal(t, 0, len(userData.Metrics), "数据应该已删除")
}

func TestUserActivityData_UpdateMetric(t *testing.T) {
	userData := model.NewUserActivityData()

	// 测试累加操作
	userData.UpdateMetric(model.MetricTypeFishWeight, 1000, model.MetricOperationAdd)
	assert.Equal(t, int64(1000), userData.Metrics[model.MetricTypeFishWeight], "累加操作应该正确")

	userData.UpdateMetric(model.MetricTypeFishWeight, 500, model.MetricOperationAdd)
	assert.Equal(t, int64(1500), userData.Metrics[model.MetricTypeFishWeight], "第二次累加应该正确")

	// 测试最大值操作
	userData.UpdateMetric(model.MetricTypeMaxSingleWeight, 800, model.MetricOperationMax)
	assert.Equal(t, int64(800), userData.MetricsMax[model.MetricTypeMaxSingleWeight], "最大值操作应该正确")

	userData.UpdateMetric(model.MetricTypeMaxSingleWeight, 600, model.MetricOperationMax)
	assert.Equal(t, int64(800), userData.MetricsMax[model.MetricTypeMaxSingleWeight], "较小值不应该更新最大值")

	userData.UpdateMetric(model.MetricTypeMaxSingleWeight, 1200, model.MetricOperationMax)
	assert.Equal(t, int64(1200), userData.MetricsMax[model.MetricTypeMaxSingleWeight], "较大值应该更新最大值")

	// 测试设置操作
	userData.UpdateMetric(model.MetricTypeFishCount, 5, model.MetricOperationSet)
	assert.Equal(t, int64(5), userData.Metrics[model.MetricTypeFishCount], "设置操作应该正确")

	userData.UpdateMetric(model.MetricTypeFishCount, 3, model.MetricOperationSet)
	assert.Equal(t, int64(3), userData.Metrics[model.MetricTypeFishCount], "第二次设置应该覆盖")
}

func TestUserActivityData_ClaimStage(t *testing.T) {
	userData := model.NewUserActivityData()
	stageId := int32(1)
	rewardConfig := `[{"item_id":1001,"count":100}]`

	// 领取阶段奖励
	userData.ClaimStage(stageId, rewardConfig)

	// 验证领取状态
	assert.True(t, userData.IsStageClaimedStage(stageId), "阶段应该已领取")
	assert.Equal(t, rewardConfig, userData.RewardConfigs[stageId], "奖励配置应该正确")
	assert.True(t, userData.ClaimedStages[stageId] > 0, "领取时间应该被设置")

	// 验证未领取的阶段
	assert.False(t, userData.IsStageClaimedStage(2), "其他阶段应该未领取")
}

func TestUserActivityData_GetClaimedStagesList(t *testing.T) {
	userData := model.NewUserActivityData()

	// 初始状态应该没有已领取阶段
	stages := userData.GetClaimedStagesList()
	assert.Equal(t, 0, len(stages), "初始状态应该没有已领取阶段")

	// 领取一些阶段
	userData.ClaimStage(1, "{}")
	userData.ClaimStage(3, "{}")
	userData.ClaimStage(2, "{}")

	// 获取已领取阶段列表
	stages = userData.GetClaimedStagesList()
	assert.Equal(t, 3, len(stages), "应该有3个已领取阶段")

	// 验证包含所有领取的阶段
	stageMap := make(map[int32]bool)
	for _, stageId := range stages {
		stageMap[stageId] = true
	}
	assert.True(t, stageMap[1], "应该包含阶段1")
	assert.True(t, stageMap[2], "应该包含阶段2")
	assert.True(t, stageMap[3], "应该包含阶段3")
}

func TestUserActivityData_GetMetricValue(t *testing.T) {
	userData := model.NewUserActivityData()

	// 测试不存在的指标
	value := userData.GetMetricValue(model.MetricTypeFishWeight)
	assert.Equal(t, int64(0), value, "不存在的指标应该返回0")

	// 测试累加类型指标
	userData.UpdateMetric(model.MetricTypeFishWeight, 1000, model.MetricOperationAdd)
	value = userData.GetMetricValue(model.MetricTypeFishWeight)
	assert.Equal(t, int64(1000), value, "累加类型指标应该正确")

	// 测试最大值类型指标
	userData.UpdateMetric(model.MetricTypeMaxSingleWeight, 800, model.MetricOperationMax)
	value = userData.GetMetricValue(model.MetricTypeMaxSingleWeight)
	assert.Equal(t, int64(800), value, "最大值类型指标应该正确")
}
