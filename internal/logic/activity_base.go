package logic

import (
	"context"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

// ActivityHandler 活动处理器接口
type ActivityHandler interface {
	// GetActivityId 获取活动ID
	GetActivityId() int64

	// HandleEvent 处理事件
	HandleEvent(ctx context.Context, playerId uint64, event *commonPB.EventCommon) error

	// GetProgress 获取活动进度
	GetProgress(ctx context.Context, playerId uint64) (interface{}, error)

	// ClaimReward 领取奖励
	ClaimReward(ctx context.Context, playerId uint64, params map[string]interface{}) error
}

//
// // ActivityManager 活动管理器 - 简单的注册和分发机制
// type ActivityManager struct {
// 	handlers map[int64]ActivityHandler
// }
//
// // NewActivityManager 创建活动管理器
// func NewActivityManager() *ActivityManager {
// 	return &ActivityManager{
// 		handlers: make(map[int64]ActivityHandler),
// 	}
// }
//
// // RegisterHandler 注册活动处理器
// func (am *ActivityManager) RegisterHandler(handler ActivityHandler) {
// 	am.handlers[handler.GetActivityId()] = handler
// }
//
// // HandleEvent 分发事件到对应的活动处理器
// func (am *ActivityManager) HandleEvent(ctx context.Context, playerId uint64, event *commonPB.EventCommon) {
// 	// 遍历所有处理器，让它们自己判断是否需要处理这个事件
// 	for _, handler := range am.handlers {
// 		// 忽略错误，继续处理其他活动
// 		_ = handler.HandleEvent(ctx, playerId, event)
// 	}
// }
//
// // GetProgress 获取指定活动的进度
// func (am *ActivityManager) GetProgress(ctx context.Context, activityId int64, playerId uint64) (interface{}, error) {
// 	if handler, exists := am.handlers[activityId]; exists {
// 		return handler.GetProgress(ctx, playerId)
// 	}
// 	return nil, nil
// }
//
// // ClaimReward 领取指定活动的奖励
// func (am *ActivityManager) ClaimReward(ctx context.Context, activityId int64, playerId uint64, params map[string]interface{}) error {
// 	if handler, exists := am.handlers[activityId]; exists {
// 		return handler.ClaimReward(ctx, playerId, params)
// 	}
// 	return nil
// }
