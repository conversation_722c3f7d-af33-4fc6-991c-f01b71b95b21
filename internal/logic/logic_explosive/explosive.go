package logic_explosive

import (
	"activitysrv/internal/dao/dao_explosive"
	"activitysrv/internal/model"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// StageConfig 阶段配置
type StageConfig struct {
	StageId    int32           `json:"stage_id"`   // 阶段ID
	Conditions map[int32]int64 `json:"conditions"` // 完成条件，key为指标类型，value为需要达到的值
	Rewards    []RewardItem    `json:"rewards"`    // 奖励列表
}

// RewardItem 奖励物品
type RewardItem struct {
	ItemId int32 `json:"item_id"` // 物品ID
	Count  int32 `json:"count"`   // 数量
}

// ExplosiveProtectionLogic 爆护之路业务逻辑
type ExplosiveProtectionLogic struct {
	cycleDAO    *dao_explosive.CycleDAO
	userDataDAO *dao_explosive.UserDataDAO
}

// NewExplosiveProtectionLogic 创建爆护之路业务逻辑实例
func NewExplosiveProtectionLogic() *ExplosiveProtectionLogic {
	return &ExplosiveProtectionLogic{
		cycleDAO:    dao_explosive.NewCycleDAO(),
		userDataDAO: dao_explosive.NewUserDataDAO(),
	}
}

// HandleWeightUpdateEvent 处理入护成功事件
func (logic *ExplosiveProtectionLogic) HandleWeightUpdateEvent(ctx context.Context, playerId uint64, event *commonPB.EventCommon) error {
	entry := logx.NewLogEntry(ctx)

	// 1. 配置过滤：检查活动是否开启
	activityCfg := cmodel.GetActivity(model.ActivityIdExplosiveProtection, consul_config.WithGrpcCtx(ctx))
	if activityCfg == nil {
		entry.Debugf("爆护之路活动配置不存在")
		return nil
	}

	// 检查活动时间是否有效
	if !logic.isActivityActive(activityCfg) {
		entry.Debugf("爆护之路活动未开启或已过期")
		return nil
	}

	// 检查事件中是否包含活动相关指标
	if !logic.hasActivityMetrics(event, activityCfg) {
		entry.Debugf("事件中不包含爆护之路活动相关指标")
		return nil
	}

	// 2. 周期管理：检查当前周期是否已结束，如果结束则自动创建新周期
	currentCycle, err := logic.cycleDAO.CheckAndCreateCycleIfNeeded(ctx, model.ActivityIdExplosiveProtection, activityCfg.CycleDays)
	if err != nil {
		return fmt.Errorf("检查或创建活动周期失败: %w", err)
	}

	// 3. 指标更新：提取事件中的指标数据并更新
	metricUpdates, err := logic.extractMetricsFromEvent(event, activityCfg)
	if err != nil {
		return fmt.Errorf("提取事件指标失败: %w", err)
	}

	if len(metricUpdates) == 0 {
		entry.Debugf("事件中没有需要更新的指标")
		return nil
	}

	// 4. 原子性更新玩家指标数据
	err = logic.userDataDAO.UpdateUserMetrics(ctx, model.ActivityIdExplosiveProtection, playerId,
		currentCycle.CycleId, metricUpdates, model.MetricOperationAdd)
	if err != nil {
		return fmt.Errorf("更新玩家指标失败: %w", err)
	}

	entry.Infof("成功处理爆护之路事件: playerId=%d, cycleId=%d, metrics=%v",
		playerId, currentCycle.CycleId, metricUpdates)

	return nil
}

// GetActivityProgress 获取活动进度
func (logic *ExplosiveProtectionLogic) GetActivityProgress(ctx context.Context, playerId uint64) (*model.ExplosiveProtectionProgress, error) {
	entry := logx.NewLogEntry(ctx)

	// 获取活动配置
	activityCfg := cmodel.GetActivity(model.ActivityIdExplosiveProtection, consul_config.WithGrpcCtx(ctx))
	if activityCfg == nil {
		return nil, fmt.Errorf("爆护之路活动配置不存在")
	}

	// 获取当前周期
	currentCycle, err := logic.cycleDAO.CheckAndCreateCycleIfNeeded(ctx, model.ActivityIdExplosiveProtection, activityCfg.CycleDays)
	if err != nil {
		return nil, fmt.Errorf("获取当前周期失败: %w", err)
	}

	// 获取当前周期的用户数据
	currentUserData, err := logic.userDataDAO.GetUserData(ctx, model.ActivityIdExplosiveProtection, playerId, currentCycle.CycleId)
	if err != nil {
		return nil, fmt.Errorf("获取当前周期用户数据失败: %w", err)
	}

	// 构建进度数据
	progress := &model.ExplosiveProtectionProgress{
		ActivityId:     model.ActivityIdExplosiveProtection,
		CurrentCycleId: currentCycle.CycleId,
		CycleEndTime:   currentCycle.EndTime,
		Metrics:        currentUserData.Metrics,
		ClaimedRecords: currentUserData.GetClaimedStagesList(),
	}

	// 获取上个周期数据（如果存在）
	if currentCycle.CycleId > 1 {
		previousCycleId := currentCycle.CycleId - 1
		previousUserData, err := logic.userDataDAO.GetUserData(ctx, model.ActivityIdExplosiveProtection, playerId, previousCycleId)
		if err != nil {
			entry.Warnf("获取上个周期用户数据失败: %v", err)
		} else {
			progress.PreviousCycle = &model.ExplosiveProtectionProgress{
				ActivityId:     model.ActivityIdExplosiveProtection,
				CurrentCycleId: previousCycleId,
				Metrics:        previousUserData.Metrics,
				ClaimedRecords: previousUserData.GetClaimedStagesList(),
			}
		}
	}

	return progress, nil
}

// ClaimStageReward 领取阶段奖励
func (logic *ExplosiveProtectionLogic) ClaimStageReward(ctx context.Context, playerId uint64, cycleId int32, stageId int32) error {
	entry := logx.NewLogEntry(ctx)

	// 获取活动配置
	activityCfg := cmodel.GetActivity(model.ActivityIdExplosiveProtection, consul_config.WithGrpcCtx(ctx))
	if activityCfg == nil {
		return fmt.Errorf("爆护之路活动配置不存在")
	}

	// 校验活动时间（奖励过期策略：仅保留上一周期）
	currentCycle, err := logic.cycleDAO.GetCurrentCycle(ctx, model.ActivityIdExplosiveProtection)
	if err != nil {
		return fmt.Errorf("获取当前周期失败: %w", err)
	}
	if currentCycle == nil {
		return fmt.Errorf("当前没有活跃的活动周期")
	}

	// 检查周期是否有效（当前周期或上一周期）
	if cycleId != currentCycle.CycleId && cycleId != currentCycle.CycleId-1 {
		return fmt.Errorf("周期 %d 的奖励已过期，仅支持当前周期和上一周期的奖励领取", cycleId)
	}

	// 获取阶段配置
	stageCfg, err := logic.getStageConfig(stageId)
	if err != nil {
		return fmt.Errorf("获取阶段配置失败: %w", err)
	}

	// 获取用户数据
	userData, err := logic.userDataDAO.GetUserData(ctx, model.ActivityIdExplosiveProtection, playerId, cycleId)
	if err != nil {
		return fmt.Errorf("获取用户数据失败: %w", err)
	}

	// 检查是否已领取
	if userData.IsStageClaimedStage(stageId) {
		return fmt.Errorf("阶段 %d 的奖励已经领取过", stageId)
	}

	// 检查是否满足领取条件
	if !logic.checkStageCondition(userData, stageCfg) {
		return fmt.Errorf("不满足阶段 %d 的领取条件", stageId)
	}

	// 原子性领取奖励
	rewardConfig := logic.buildRewardConfig(stageCfg)
	err = logic.userDataDAO.ClaimReward(ctx, model.ActivityIdExplosiveProtection, playerId, cycleId, stageId, rewardConfig)
	if err != nil {
		return fmt.Errorf("领取奖励失败: %w", err)
	}

	entry.Infof("成功领取爆护之路奖励: playerId=%d, cycleId=%d, stageId=%d", playerId, cycleId, stageId)

	// TODO: 调用大厅服发奖接口
	// err = logic.sendRewardToHall(ctx, playerId, stageCfg.Rewards)
	// if err != nil {
	//     entry.Errorf("发放奖励失败: %v", err)
	//     // 记录失败日志用于后续补偿，但不回滚已领取状态
	// }

	return nil
}

// CheckRedDot 检查红点状态
func (logic *ExplosiveProtectionLogic) CheckRedDot(ctx context.Context, playerId uint64) (bool, error) {
	// 获取活动进度
	progress, err := logic.GetActivityProgress(ctx, playerId)
	if err != nil {
		return false, err
	}

	// 获取所有阶段配置
	stageConfigs, err := logic.getAllStageConfigs()
	if err != nil {
		return false, err
	}

	// 检查当前周期是否有可领取的奖励
	hasAvailableReward := logic.checkAvailableRewards(progress.Metrics, progress.ClaimedRecords, stageConfigs)
	if hasAvailableReward {
		return true, nil
	}

	// 检查上个周期是否有可领取的奖励
	if progress.PreviousCycle != nil {
		hasAvailableReward = logic.checkAvailableRewards(progress.PreviousCycle.Metrics,
			progress.PreviousCycle.ClaimedRecords, stageConfigs)
		if hasAvailableReward {
			return true, nil
		}
	}

	return false, nil
}

// getStageConfig 获取阶段配置
func (logic *ExplosiveProtectionLogic) getStageConfig(stageId int32) (*StageConfig, error) {
	// 这里应该从配置中心或数据库获取阶段配置
	// 暂时使用硬编码的配置作为示例
	stageConfigs := logic.getHardcodedStageConfigs()

	for _, config := range stageConfigs {
		if config.StageId == stageId {
			return config, nil
		}
	}

	return nil, fmt.Errorf("阶段配置不存在: stageId=%d", stageId)
}

// getAllStageConfigs 获取所有阶段配置
func (logic *ExplosiveProtectionLogic) getAllStageConfigs() ([]*StageConfig, error) {
	// 这里应该从配置中心或数据库获取所有阶段配置
	// 暂时使用硬编码的配置作为示例
	return logic.getHardcodedStageConfigs(), nil
}

// getHardcodedStageConfigs 获取硬编码的阶段配置（示例）
func (logic *ExplosiveProtectionLogic) getHardcodedStageConfigs() []*StageConfig {
	return []*StageConfig{
		{
			StageId: 1,
			Conditions: map[int32]int64{
				model.MetricTypeFishWeight: 1000, // 累计鱼重量达到1000
			},
			Rewards: []RewardItem{
				{ItemId: 1001, Count: 10}, // 金币 x10
			},
		},
		{
			StageId: 2,
			Conditions: map[int32]int64{
				model.MetricTypeFishWeight: 5000, // 累计鱼重量达到5000
			},
			Rewards: []RewardItem{
				{ItemId: 1001, Count: 50}, // 金币 x50
			},
		},
		{
			StageId: 3,
			Conditions: map[int32]int64{
				model.MetricTypeFishWeight:      10000, // 累计鱼重量达到10000
				model.MetricTypeMaxSingleWeight: 500,   // 单次最大重量达到500
			},
			Rewards: []RewardItem{
				{ItemId: 1001, Count: 100}, // 金币 x100
				{ItemId: 2001, Count: 1},   // 特殊道具 x1
			},
		},
		{
			StageId: 4,
			Conditions: map[int32]int64{
				model.MetricTypeFishWeight: 20000, // 累计鱼重量达到20000
				model.MetricTypeFishCount:  100,   // 累计鱼数量达到100
			},
			Rewards: []RewardItem{
				{ItemId: 1001, Count: 200}, // 金币 x200
				{ItemId: 2002, Count: 1},   // 高级道具 x1
			},
		},
		{
			StageId: 5,
			Conditions: map[int32]int64{
				model.MetricTypeFishWeight:      50000, // 累计鱼重量达到50000
				model.MetricTypeMaxSingleWeight: 1000,  // 单次最大重量达到1000
			},
			Rewards: []RewardItem{
				{ItemId: 1001, Count: 500}, // 金币 x500
				{ItemId: 3001, Count: 1},   // 稀有道具 x1
			},
		},
	}
}

// extractMetricsFromEvent 从事件中提取指标数据
func (logic *ExplosiveProtectionLogic) extractMetricsFromEvent(event *commonPB.EventCommon, cfg *cmodel.Activity) (map[int32]int64, error) {
	metrics := make(map[int32]int64)

	if event.EventType == commonPB.EVENT_TYPE_ET_FISH_GET {
		// 提取鱼重量
		if weight, exists := event.IntData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_WEIGHT)]; exists {
			metrics[model.MetricTypeFishWeight] = weight
			metrics[model.MetricTypeMaxSingleWeight] = weight // 用于最大值比较
		}

		// 提取鱼数量（默认为1）
		metrics[model.MetricTypeFishCount] = 1 // 默认捕获1条鱼
	}

	return metrics, nil
}

// checkStageCondition 检查阶段完成条件
func (logic *ExplosiveProtectionLogic) checkStageCondition(userData *model.UserActivityData, stageCfg *StageConfig) bool {
	for metricType, requiredValue := range stageCfg.Conditions {
		currentValue := userData.GetMetricValue(metricType)
		if currentValue < requiredValue {
			return false
		}
	}
	return true
}

// buildRewardConfig 构建奖励配置
func (logic *ExplosiveProtectionLogic) buildRewardConfig(stageCfg *StageConfig) string {
	rewardData, _ := json.Marshal(stageCfg.Rewards)
	return string(rewardData)
}

// isActivityActive 检查活动是否活跃
func (logic *ExplosiveProtectionLogic) isActivityActive(cfg *cmodel.Activity) bool {
	// 检查活动时间
	now := time.Now().Unix()
	if cfg.OpenAt > 0 && now < cfg.OpenAt {
		return false
	}
	if cfg.CloseAt > 0 && now > cfg.CloseAt {
		return false
	}

	return true
}

// hasActivityMetrics 检查事件是否包含活动相关指标
func (logic *ExplosiveProtectionLogic) hasActivityMetrics(event *commonPB.EventCommon, cfg *cmodel.Activity) bool {
	// 检查是否是入护事件
	if event.EventType == commonPB.EVENT_TYPE_ET_FISH_GET {
		// 检查是否包含鱼重量数据
		if _, exists := event.IntData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_WEIGHT)]; exists {
			return true
		}
	}

	return false
}

// checkAvailableRewards 检查是否有可领取的奖励
func (logic *ExplosiveProtectionLogic) checkAvailableRewards(metrics map[int32]int64, claimedRecords []int32, stageConfigs []*StageConfig) bool {
	// 构建已领取阶段的map
	claimedMap := make(map[int32]bool)
	for _, stageId := range claimedRecords {
		claimedMap[stageId] = true
	}

	// 检查每个阶段
	for _, stageCfg := range stageConfigs {
		// 如果已经领取过，跳过
		if claimedMap[stageCfg.StageId] {
			continue
		}

		// 检查是否满足条件
		canClaim := true
		for metricType, requiredValue := range stageCfg.Conditions {
			currentValue := metrics[metricType]
			if currentValue < requiredValue {
				canClaim = false
				break
			}
		}

		if canClaim {
			return true
		}
	}

	return false
}

// 实现ActivityHandler接口

// GetActivityId 获取活动ID
func (logic *ExplosiveProtectionLogic) GetActivityId() int64 {
	return model.ActivityIdExplosiveProtection
}

// HandleEvent 处理事件 - 实现ActivityHandler接口
func (logic *ExplosiveProtectionLogic) HandleEvent(ctx context.Context, playerId uint64, event *commonPB.EventCommon) error {
	return logic.HandleWeightUpdateEvent(ctx, playerId, event)
}

// GetProgress 获取活动进度 - 实现ActivityHandler接口
func (logic *ExplosiveProtectionLogic) GetProgress(ctx context.Context, playerId uint64) (interface{}, error) {
	return logic.GetActivityProgress(ctx, playerId)
}

// ClaimReward 领取奖励 - 实现ActivityHandler接口
func (logic *ExplosiveProtectionLogic) ClaimReward(ctx context.Context, playerId uint64, params map[string]interface{}) error {
	// 从参数中提取cycleId和stageId
	cycleId, ok := params["cycleId"].(int32)
	if !ok {
		return fmt.Errorf("缺少cycleId参数")
	}

	stageId, ok := params["stageId"].(int32)
	if !ok {
		return fmt.Errorf("缺少stageId参数")
	}

	return logic.ClaimStageReward(ctx, playerId, cycleId, stageId)
}

// GetStageConfig 获取阶段配置（公开方法）
func (logic *ExplosiveProtectionLogic) GetStageConfig(stageId int32) (*StageConfig, error) {
	return logic.getStageConfig(stageId)
}

// GetAllStageConfigs 获取所有阶段配置（公开方法）
func (logic *ExplosiveProtectionLogic) GetAllStageConfigs() ([]*StageConfig, error) {
	return logic.getAllStageConfigs()
}
